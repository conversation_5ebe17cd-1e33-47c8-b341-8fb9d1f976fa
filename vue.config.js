const path = require('path')

module.exports = {
    css: {
        loaderOptions: {
            postcss: {
                plugins: [
                    require("tailwindcss"),
                    require("autoprefixer")
                ],
            },
        },
    },
    // 配置webpack
    configureWebpack: config => {
        if (process.env.NODE_ENV === 'production') {
            // 生产环境配置
            config.mode = 'production'
            config.optimization = {
                minimize: true,
                splitChunks: {
                    chunks: 'all',
                    cacheGroups: {
                        vendor: {
                            name: 'codepass-vendor',
                            test: /[\\/]node_modules[\\/]/,
                            priority: 10,
                            chunks: 'all'
                        },
                        common: {
                            name: 'codepass-common',
                            minChunks: 2,
                            priority: 5,
                            chunks: 'all'
                        }
                    }
                }
            }

            // 配置输出文件名，添加codepass前缀
            config.output = {
                ...config.output,
                filename: 'js/codepass-[name].[contenthash:8].js',
                chunkFilename: 'js/codepass-[name].[contenthash:8].js'
            }

            // 配置CSS文件名
            if (config.plugins) {
                config.plugins.forEach(plugin => {
                    if (plugin.constructor.name === 'MiniCssExtractPlugin') {
                        plugin.options.filename = 'css/codepass-[name].[contenthash:8].css'
                        plugin.options.chunkFilename = 'css/codepass-[name].[contenthash:8].css'
                    }
                })
            }

        } else {
            // 开发环境配置
            config.mode = 'development'
            config.devtool = 'source-map'
        }
    },

    // 配置静态资源
    chainWebpack: config => {
        if (process.env.NODE_ENV === 'production') {
            // 配置图片资源文件名
            if (config.module.rule('images').uses.has('url-loader')) {
                config.module
                    .rule('images')
                    .use('url-loader')
                    .tap(options => {
                        options = options || {}
                        options.name = 'img/codepass-[name].[contenthash:8].[ext]'
                        return options
                    })
            }

            // 配置字体文件名
            if (config.module.rule('fonts').uses.has('url-loader')) {
                config.module
                    .rule('fonts')
                    .use('url-loader')
                    .tap(options => {
                        options = options || {}
                        options.name = 'fonts/codepass-[name].[contenthash:8].[ext]'
                        return options
                    })
            }

            // 配置媒体文件名
            if (config.module.rule('media').uses.has('url-loader')) {
                config.module
                    .rule('media')
                    .use('url-loader')
                    .tap(options => {
                        options = options || {}
                        options.name = 'media/codepass-[name].[contenthash:8].[ext]'
                        return options
                    })
            }

            // 禁用source map生成，避免.map文件
            config.devtool(false)

            // 禁用source map相关插件，避免.map文件
            config.plugins.delete('preload')
            config.plugins.delete('prefetch')

            // 自定义插件来清理不需要的文件
            config.plugin('custom-clean')
                .use(class CustomCleanPlugin {
                    constructor() {
                        this.basePath = path.resolve(process.cwd())
                    }

                    apply(compiler) {
                        compiler.hooks.afterEmit.tap('CustomCleanPlugin', (compilation) => {
                            // 清理以点开头的文件和.map文件
                            const outputPath = compilation.outputOptions.path
                            if (outputPath && require('fs').existsSync(outputPath)) {
                                this.cleanDirectory(outputPath)
                            }
                        })
                    }

                    // 验证路径安全性
                    validatePath(filePath) {
                        const normalizedPath = path.resolve(filePath)
                        return normalizedPath.startsWith(this.basePath)
                    }

                    cleanDirectory(dir) {
                        const fs = require('fs')
                        const path = require('path')

                        // 验证目录路径安全性
                        const normalizedDir = path.resolve(dir)
                        if (!this.validatePath(normalizedDir)) {
                            console.error(`不安全的目录路径: ${dir}`)
                            return
                        }

                        try {
                            const files = fs.readdirSync(normalizedDir)
                            files.forEach(file => {
                                const filePath = path.resolve(normalizedDir, file)

                                // 验证文件路径安全性
                                if (!this.validatePath(filePath)) {
                                    console.error(`不安全的文件路径: ${filePath}`)
                                    return
                                }

                                const stat = fs.statSync(filePath)

                                if (stat.isDirectory()) {
                                    this.cleanDirectory(filePath)
                                } else if (file.startsWith('.') || file.endsWith('.map')) {
                                    try {
                                        fs.unlinkSync(filePath)
                                        console.log(`已删除文件: ${filePath}`)
                                    } catch (error) {
                                        console.warn(`删除文件失败: ${filePath}`, error.message)
                                    }
                                }
                            })
                        } catch (error) {
                            console.warn(`清理目录失败: ${normalizedDir}`, error.message)
                        }
                    }
                })
        }
    }
}

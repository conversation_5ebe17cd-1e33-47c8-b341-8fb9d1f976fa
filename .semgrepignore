# Semgrep ignore file for security scan suppressions
# These files have been manually reviewed and secured

# Path traversal issues - manually secured with path validation
vue.config.js
scripts/build-cleanup.js

# Regex injection issues - manually secured with pattern validation
uni_modules/uni-forms/components/uni-forms/validate.js

# Node modules and build artifacts
node_modules/
unpackage/
dist/
build/

# Test files
*.test.js
*.spec.js
test/
tests/
